# Telegram Group Creator Script

An enhanced Python script for creating multiple Telegram groups with advanced features and improved user experience.

## Features

### ✨ New Enhancements

1. **🌍 Full English Translation**
   - All Arabic text converted to English
   - User-friendly prompts and messages
   - Clear error messages and status updates

2. **💬 Message Sending Feature**
   - Optional message sending after group creation
   - Multiple message types:
     - Welcome message with date
     - Custom user-defined messages
     - No message option
   - Robust error handling for message failures

3. **📊 Improved Logging**
   - Clean, essential logging only
   - Progress tracking with visual indicators
   - Detailed operation summary
   - Better error reporting

4. **🎨 Visual Enhancements**
   - Colorized output for better readability
   - Different colors for different message types:
     - 🟢 Green: Success messages
     - 🔴 Red: Error messages
     - 🟡 Yellow: Warning messages
     - 🔵 Cyan: Information messages
     - 🟣 Magenta: User prompts
   - Animated spinner for progress indication
   - Professional formatting

5. **👥 Session Management**
   - Custom session name input
   - Support for multiple simultaneous sessions
   - Prevents conflicts when running multiple instances
   - Default session fallback option

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Configure your API credentials in the script:
   - `API_ID`: Your Telegram API ID
   - `API_HASH`: Your Telegram API Hash

## Usage

1. Run the script:
```bash
python create.py
```

2. Follow the interactive prompts:
   - Enter a custom session name (optional)
   - Choose whether to send messages
   - Configure message content if enabled
   - Specify number of groups to create
   - Set group name and description

## Configuration Options

### Session Management
- **Custom Session**: Enter a unique name for your session
- **Default Session**: Press Enter to use the default session name

### Message Options
- **Welcome with Date**: Automatic welcome message with creation date
- **Custom Message**: Enter your own message content
- **No Message**: Create groups without sending messages

### Group Settings
- **Group Name**: Customizable group title
- **Group Description**: Customizable group description
- **Quantity**: Number of groups to create

## Error Handling

The script includes robust error handling for:
- **Rate Limiting**: Automatic waiting with countdown display
- **Network Issues**: Graceful error reporting and retry logic
- **Authentication Errors**: Clear error messages for login issues
- **Message Failures**: Continues group creation even if messages fail

## Output Features

- **Real-time Progress**: Live progress indicator with spinner
- **Color-coded Status**: Easy-to-read status messages
- **Operation Summary**: Detailed report of successful/failed operations
- **Professional Formatting**: Clean, organized output

## Requirements

- Python 3.7+
- pyrogram
- colorama
- Valid Telegram API credentials
- Active internet connection

## Notes

- The script creates supergroups (not basic groups)
- Rate limiting is automatically handled
- Multiple sessions can run simultaneously with different session names
- All operations are logged with appropriate status indicators
