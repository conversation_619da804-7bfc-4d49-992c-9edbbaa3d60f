import asyncio
import datetime
import time
import sys
import os
from pyrogram import Client, errors
from colorama import Fore, Back, Style, init

# Initialize colorama for cross-platform colored output
init(autoreset=True)

# Default session configuration
DEFAULT_SESSION_NAME = "telegram_group_creator"
API_ID = 3656868
API_HASH = "3911d017831e5325cf9f02edbb3bcae1"

# Color constants for better readability
class Colors:
    SUCCESS = Fore.GREEN
    ERROR = Fore.RED
    WARNING = Fore.YELLOW
    INFO = Fore.CYAN
    PROMPT = Fore.MAGENTA
    RESET = Style.RESET_ALL
    BOLD = Style.BRIGHT


def get_session_name():
    """Get custom session name from user to allow multiple sessions"""
    print(f"{Colors.INFO}Session Management{Colors.RESET}")
    print("-" * 20)

    session_name = input(f"{Colors.PROMPT}Enter a custom session name (or press Enter for default): {Colors.RESET}").strip()

    if not session_name:
        session_name = DEFAULT_SESSION_NAME
        print(f"{Colors.INFO}Using default session: {session_name}{Colors.RESET}")
    else:
        print(f"{Colors.INFO}Using custom session: {session_name}{Colors.RESET}")

    return session_name

def get_message_preference():
    """Ask user if they want to send messages after group creation"""
    print(f"\n{Colors.INFO}Message Sending Options{Colors.RESET}")
    print("-" * 25)

    while True:
        choice = input(f"{Colors.PROMPT}Do you want to send messages after creating groups? (y/n): {Colors.RESET}").strip().lower()
        if choice in ['y', 'yes']:
            return True
        elif choice in ['n', 'no']:
            return False
        else:
            print(f"{Colors.WARNING}Please enter 'y' for yes or 'n' for no.{Colors.RESET}")

def get_message_content():
    """Get message content from user"""
    print(f"\n{Colors.INFO}Message Content Configuration{Colors.RESET}")
    print("-" * 35)

    message_types = {
        "1": "Welcome message with date",
        "2": "Custom message",
        "3": "No message (group creation only)"
    }

    print("Available message types:")
    for key, value in message_types.items():
        print(f"  {key}. {value}")

    while True:
        choice = input(f"{Colors.PROMPT}Select message type (1-3): {Colors.RESET}").strip()
        if choice == "1":
            today_date = datetime.date.today().strftime("%Y-%m-%d")
            return f"Welcome to the group! Created on {today_date}"
        elif choice == "2":
            custom_message = input(f"{Colors.PROMPT}Enter your custom message: {Colors.RESET}").strip()
            return custom_message if custom_message else "Welcome to the group!"
        elif choice == "3":
            return None
        else:
            print(f"{Colors.WARNING}Please enter a number between 1 and 3.{Colors.RESET}")

async def main():
    print(f"{Colors.BOLD}{Colors.INFO}🚀 Telegram Group Creator Script{Colors.RESET}")
    print(f"{Colors.INFO}{'=' * 40}{Colors.RESET}\n")

    # Get session name for multiple session support
    session_name = get_session_name()

    # Get message sending preference
    send_messages = get_message_preference()
    message_content = None

    if send_messages:
        message_content = get_message_content()

    print(f"\n{Colors.INFO}Initializing Telegram client...{Colors.RESET}")

    try:
        app = Client(session_name, API_ID, API_HASH)
        await app.start()
        user_info = await app.get_me()
        print(f"{Colors.SUCCESS}✅ Successfully logged in as: @{user_info.username}{Colors.RESET}\n")
    except errors.UserIsBot:
        print(f"{Colors.ERROR}❌ Error: Bots cannot be used with this script. Please use a regular user account.{Colors.RESET}")
        return
    except Exception as e:
        print(f"{Colors.ERROR}❌ Error starting client: {e}{Colors.RESET}")
        print(f"{Colors.WARNING}Please ensure Pyrogram is installed correctly and you have internet connection.{Colors.RESET}")
        return

    # Get number of groups to create
    while True:
        try:
            num_groups_str = input(f"{Colors.PROMPT}Enter the number of groups to create (or 'q' to quit): {Colors.RESET}")
            if num_groups_str.lower() == 'q':
                print(f"{Colors.INFO}Operation cancelled by user.{Colors.RESET}")
                await app.stop()
                return
            num_groups = int(num_groups_str)
            if num_groups <= 0:
                print(f"{Colors.WARNING}Number must be greater than zero. Please try again.{Colors.RESET}")
                continue
            break
        except ValueError:
            print(f"{Colors.ERROR}Invalid input. Please enter a valid number.{Colors.RESET}")

    # Get group name
    group_name = input(f"{Colors.PROMPT}Enter group name (or press Enter for '2025'): {Colors.RESET}").strip()
    if not group_name:
        group_name = "2025"

    # Get group description
    group_description = input(f"{Colors.PROMPT}Enter group description (or press Enter for default): {Colors.RESET}").strip()
    if not group_description:
        group_description = group_name

    print(f"\n{Colors.INFO}📋 Configuration Summary:{Colors.RESET}")
    print(f"   • Groups to create: {Colors.BOLD}{num_groups}{Colors.RESET}")
    print(f"   • Group name: {Colors.BOLD}{group_name}{Colors.RESET}")
    print(f"   • Group description: {Colors.BOLD}{group_description}{Colors.RESET}")
    print(f"   • Send messages: {Colors.BOLD}{'Yes' if send_messages else 'No'}{Colors.RESET}")
    if send_messages and message_content:
        print(f"   • Message: {Colors.BOLD}{message_content[:50]}{'...' if len(message_content) > 50 else ''}{Colors.RESET}")

    print(f"\n{Colors.INFO}{'=' * 50}{Colors.RESET}")
    print(f"{Colors.INFO}🚀 Starting group creation process...{Colors.RESET}")
    print(f"{Colors.INFO}{'=' * 50}{Colors.RESET}")

    # Progress tracking
    spinner_frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
    spinner_index = 0
    successful_groups = 0
    failed_groups = 0

    for i in range(num_groups):
        group_number = i + 1

        # Show progress with spinner
        sys.stdout.write(f"\r{Colors.INFO}Creating group {group_number}/{num_groups} {spinner_frames[spinner_index]} {Colors.RESET}")
        sys.stdout.flush()
        spinner_index = (spinner_index + 1) % len(spinner_frames)

        start_time = time.time()
        try:
            # Create the supergroup
            new_group = await app.create_supergroup(
                title=group_name,
                description=group_description
            )

            elapsed_time = time.time() - start_time

            # Warn if creation took too long
            if elapsed_time > 3:
                print(f"\r{Colors.WARNING}⚠️  Group {group_number} creation was slow ({elapsed_time:.2f}s). Continuing...{Colors.RESET}")
                await asyncio.sleep(1)

            # Send message if requested
            if send_messages and message_content:
                try:
                    await app.send_message(new_group.id, message_content)
                    message_status = "with message"
                except Exception as msg_error:
                    print(f"\r{Colors.WARNING}⚠️  Group {group_number} created but message failed: {msg_error}{Colors.RESET}")
                    message_status = "message failed"
            else:
                message_status = "no message"

            successful_groups += 1
            print(f"\r{Colors.SUCCESS}✅ Group {group_number}/{num_groups} created successfully ({message_status}) - ID: {new_group.id}{Colors.RESET}")

        except errors.FloodWait as e:
            wait_time = e.value
            print(f"\r{Colors.WARNING}🚨 Telegram rate limit reached. Waiting {wait_time} seconds...{Colors.RESET}")

            # Show countdown
            for remaining in range(wait_time, 0, -1):
                sys.stdout.write(f"\r{Colors.WARNING}⏳ Waiting... {remaining}s remaining{Colors.RESET}")
                sys.stdout.flush()
                await asyncio.sleep(1)

            print(f"\r{Colors.INFO}✅ Wait complete. Resuming...{Colors.RESET}")
            # Retry this group
            i -= 1
            continue

        except Exception as e:
            failed_groups += 1
            print(f"\r{Colors.ERROR}❌ Failed to create group {group_number}/{num_groups}: {e}{Colors.RESET}")
            await asyncio.sleep(2)

    # Final summary
    print(f"\n{Colors.INFO}{'=' * 50}{Colors.RESET}")
    print(f"{Colors.BOLD}{Colors.INFO}📊 Operation Summary{Colors.RESET}")
    print(f"{Colors.INFO}{'=' * 50}{Colors.RESET}")
    print(f"{Colors.SUCCESS}✅ Successfully created: {successful_groups} groups{Colors.RESET}")
    if failed_groups > 0:
        print(f"{Colors.ERROR}❌ Failed to create: {failed_groups} groups{Colors.RESET}")
    print(f"{Colors.INFO}📱 Total requested: {num_groups} groups{Colors.RESET}")

    if successful_groups == num_groups:
        print(f"\n{Colors.SUCCESS}{Colors.BOLD}🎉 All groups created successfully!{Colors.RESET}")
    elif successful_groups > 0:
        print(f"\n{Colors.WARNING}⚠️  Partial success: {successful_groups}/{num_groups} groups created{Colors.RESET}")
    else:
        print(f"\n{Colors.ERROR}❌ No groups were created successfully{Colors.RESET}")

    print(f"\n{Colors.INFO}Closing Telegram connection...{Colors.RESET}")
    await app.stop()
    print(f"{Colors.SUCCESS}✅ Connection closed successfully.{Colors.RESET}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}⚠️  Operation cancelled by user.{Colors.RESET}")
    except Exception as e:
        print(f"{Colors.ERROR}❌ Unexpected error occurred: {e}{Colors.RESET}")
        print(f"{Colors.INFO}Please check your internet connection and try again.{Colors.RESET}")
