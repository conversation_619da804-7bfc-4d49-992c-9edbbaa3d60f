import asyncio
import datetime
import time
import sys
import os
from pyrogram import Client, errors

SESSION_NAME = "testoooo"
API_ID = 3656868
API_HASH = "3911d017831e5325cf9f02edbb3bcae1"


async def main():

    print("Pyrogram Group Creator Script")
    print("-----------------------------\n")

    try:
        app = Client(SESSION_NAME, API_ID, API_HASH)
        await app.start()
        print(f"تم تسجيل الدخول بنجاح كـ: @{(await app.get_me()).username}\n")
    except errors.UserIsBot:
        print("خطأ: لا يمكن استخدام البوتات مع هذا السكربت، يرجى استخدام حساب مستخدم عادي.")
        return
    except Exception as e:
        print(f"حدث خطأ أثناء بدء تشغيل العميل: {e}")
        print("يرجى التأكد من أنك قمت بتثبيت Pyrogram بشكل صحيح وأن لديك اتصال بالإنترنت.")
        return

    while True:
        try:
            num_groups_str = input("أدخل عدد الجروبات التي تريد إنشاءها (أو 'q' للخروج): ")
            if num_groups_str.lower() == 'q':
                break
            num_groups = int(num_groups_str)
            if num_groups <= 0:
                print("العدد يجب أن يكون أكبر من صفر. يرجى المحاولة مرة أخرى.")
                continue
            break
        except ValueError:
            print("إدخال غير صالح. يرجى إدخال رقم صحيح.")

    print(f"\nسأقوم بإنشاء {num_groups} جروب باسم '2025'.")
    print("------------------------------------------")

    spinner_frames = ['|', '/', '-', '\\']
    spinner_index = 0

    for i in range(num_groups):
        group_number = i + 1
        sys.stdout.write(f"\rإنشاء الجروب رقم {group_number}/{num_groups} {spinner_frames[spinner_index]} ")
        sys.stdout.flush()
        spinner_index = (spinner_index + 1) % len(spinner_frames)

        start_time = time.time()
        try:

            new_group = await app.create_supergroup(
                title="2025",
                description="2025"
            )

            elapsed_time = time.time() - start_time

            if elapsed_time > 3:
                sys.stdout.write(
                    f"\r⚠️ يوجد إبطاء في إنشاء الجروب رقم {group_number}. استغرق {elapsed_time:.2f} ثانية. سأكمل...\n")
                sys.stdout.flush()
                await asyncio.sleep(1)

            today_date = datetime.date.today().strftime("%Y-%m-%d")
            message_text = f"جروب تم انشائه ({today_date})"
            await app.send_message(new_group.id, message_text)

            sys.stdout.write(
                f"\r✅ تم إنشاء الجروب رقم {group_number}/{num_groups} بنجاح وإرسال الرسالة. (ID: {new_group.id})\n")
            sys.stdout.flush()

        except errors.FloodWait as e:

            wait_time = e.value
            sys.stdout.write(f"\r🚨 تم الوصول إلى حد التيليجرام (Flood Wait). يرجى الانتظار {wait_time} ثانية.\n")
            sys.stdout.flush()
            await asyncio.sleep(wait_time + 1)

            continue
        except Exception as e:
            sys.stdout.write(f"\r❌ فشل إنشاء الجروب رقم {group_number}/{num_groups}: {e}\n")
            sys.stdout.flush()
            await asyncio.sleep(2)

    print("\n------------------------------------------")
    print("✅ تم الانتهاء من إنشاء جميع الجروبات المطلوبة.")
    await app.stop()
    print("تم إغلاق اتصال Pyrogram.")


if __name__ == "__main__":

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nالعملية تم إيقافها بواسطة المستخدم.")
    except Exception as e:
        print(f"حدث خطأ غير متوقع: {e}")
